<!--
Ti<PERSON><PERSON> ích n<PERSON>y đ<PERSON><PERSON><PERSON> làm bởi Hung Vu : fb.com/hungvu25
-->
<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  <style>
    body {
      width: 350px;
      padding: 20px;
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
      margin: 0;
    }
    
    h1 {
      font-size: 18px;
      margin-bottom: 15px;
      color: #1a73e8;
      text-align: center;
    }
    
    .step {
      background: #f8f9fa;
      padding: 12px;
      margin: 10px 0;
      border-radius: 8px;
      border-left: 4px solid #1a73e8;
    }
    
    .step-title {
      font-weight: bold;
      color: #202124;
      margin-bottom: 5px;
    }
    
    .step-desc {
      font-size: 14px;
      color: #5f6368;
      line-height: 1.4;
    }
    
    button {
      width: 100%;
      padding: 12px;
      background: linear-gradient(135deg, #1a73e8, #1557b0);
      color: white;
      border: none;
      border-radius: 6px;
      font-size: 14px;
      font-weight: 500;
      cursor: pointer;
      transition: all 0.3s ease;
      margin-bottom: 8px;
    }
    
    .button-container {
      display: flex;
      flex-direction: column;
      gap: 8px;
      margin: 15px 0;
    }
    
    .verify-btn {
      background: linear-gradient(135deg, #9c27b0, #7b1fa2) !important;
      font-weight: 600;
    }
    
    .extract-btn {
      background: linear-gradient(135deg, #ff9800, #f57c00) !important;
      font-weight: 600;
    }
    
    .card-generator-btn {
      background: linear-gradient(135deg, #4CAF50, #388E3C) !important;
      font-weight: 600;
    }
    
    .extract-btn:hover {
      background: linear-gradient(135deg, #ffb74d, #ff9800) !important;
      transform: translateY(-1px);
      box-shadow: 0 4px 12px rgba(255, 152, 0, 0.3);
    }
    
    .card-generator-btn:hover {
      background: linear-gradient(135deg, #66BB6A, #4CAF50) !important;
      transform: translateY(-1px);
      box-shadow: 0 4px 12px rgba(76, 175, 80, 0.3);
    }
    
    .verify-btn:hover {
      background: linear-gradient(135deg, #ad42c4, #8e24aa) !important;
      transform: translateY(-1px);
      box-shadow: 0 4px 12px rgba(156, 39, 176, 0.3);
    }
    
    button:hover {
      background: linear-gradient(135deg, #1557b0, #1a73e8);
      transform: translateY(-1px);
      box-shadow: 0 4px 12px rgba(26, 115, 232, 0.3);
    }
    
    button:disabled {
      background: #ccc;
      cursor: not-allowed;
    }
    
    .status {
      padding: 8px 12px;
      margin-top: 10px;
      border-radius: 6px;
      font-size: 13px;
      text-align: center;
      font-weight: 500;
      display: none;
      transition: all 0.3s ease;
    }
    
    .status.success {
      background: linear-gradient(135deg, #4CAF50, #45a049);
      color: white;
      box-shadow: 0 2px 8px rgba(76, 175, 80, 0.3);
    }
    
    .status.error {
      background: linear-gradient(135deg, #f44336, #d32f2f);
      color: white;
      box-shadow: 0 2px 8px rgba(244, 67, 54, 0.3);
    }
    
    .status.info {
      background: linear-gradient(135deg, #2196F3, #1976D2);
      color: white;
      box-shadow: 0 2px 8px rgba(33, 150, 243, 0.3);
    }
    
    /* Auto-filled field styling */
    input[style*="background-color: #e8f5e8"] {
      animation: autoFillGlow 2s ease-in-out;
    }
    
    @keyframes autoFillGlow {
      0% { box-shadow: 0 0 5px rgba(76, 175, 80, 0.5); }
      50% { box-shadow: 0 0 20px rgba(76, 175, 80, 0.8); }
      100% { box-shadow: 0 0 5px rgba(76, 175, 80, 0.5); }
    }
    
    @keyframes pulse {
      0% { transform: scale(1); box-shadow: 0 4px 12px rgba(156, 39, 176, 0.3); }
      50% { transform: scale(1.05); box-shadow: 0 6px 20px rgba(156, 39, 176, 0.6); }
      100% { transform: scale(1); box-shadow: 0 4px 12px rgba(156, 39, 176, 0.3); }
    }
    
    .status.info {
      background: #e3f2fd;
      color: #1565c0;
      border: 1px solid #90caf9;
    }
    
    .config {
      margin: 15px 0;
    }
    
    .config label {
      display: block;
      margin: 8px 0 4px 0;
      font-weight: 500;
      color: #202124;
    }
    
    .config input {
      width: 100%;
      padding: 8px;
      border: 1px solid #dadce0;
      border-radius: 4px;
      box-sizing: border-box;
    }
    
    .config input:focus {
      outline: none;
      border-color: #1a73e8;
      box-shadow: 0 0 0 2px rgba(26, 115, 232, 0.2);
    }
  </style>
</head>
<body>
  <h1>🎓 Student Card Verifier</h1>
  
  <div class="config">
    <label for="school">Tên trường:</label>
    <input type="text" id="school" value="Đại học Bách khoa TP.HCM">
    
    <label for="firstName">Tên:</label>
    <input type="text" id="firstName" value="Lan">
    
    <label for="lastName">Họ:</label>
    <input type="text" id="lastName" value="Phuong">
    
    <label for="dateOfBirth">Ngày sinh:</label>
    <input type="date" id="dateOfBirth" value="">
    
    <label for="email">Email:</label>
    <input type="email" id="email" value="<EMAIL>">
  </div>
  
  <div class="button-container">
    <button id="extractBtn" class="extract-btn">📋 Extract Info from Website</button>
    <button id="directVerifyBtn" class="verify-btn">🔐 Verify Google One</button>
    <button id="cardGeneratorBtn" class="card-generator-btn">🎓 Open Card Generator</button>
  </div>
  
  <div id="status"></div>
  
  <div class="step">
    <div class="step-title">📌 Bước 1: Create Student Card</div>
    <div class="step-desc">Click "🎓 Open Card Generator" để tạo student card hoặc sử dụng bất kỳ trang web nào có form student</div>
  </div>
  
  <div class="step">
    <div class="step-title">📌 Bước 2: Extract Student Info</div>
    <div class="step-desc">Click "📋 Extract Info from Website" để tự động lấy thông tin từ form hoặc student card</div>
  </div>
  
  <div class="step">
    <div class="step-title">📌 Bước 3: Start Verification</div>
    <div class="step-desc">Click "🔐 Verify Google One" để bắt đầu verification với thông tin đã extract</div>
  </div>
  
  <div class="step">
    <div class="step-title">📌 Bước 4: Tự động xử lý</div>
    <div class="step-desc">Extension tự động mở Google One và điền form SheerID</div>
  </div>

  <script src="popup.js"></script>
</body>
</html>
